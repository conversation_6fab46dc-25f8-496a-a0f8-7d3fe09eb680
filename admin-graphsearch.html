<?php
/**
 * GraphSearch Application - Admin Interface
 *
 * LinkedIn search application integrated with admin authentication system.
 * Requires admin authentication to access.
 */

require_once __DIR__ . '/bootstrap.php';

// Require authentication
if (!$auth->isAuthenticated()) {
    redirectWithMessage('login.php', 'Please log in to access GraphSearch.', 'error');
}

// Check role-based access (admin or superadmin only)
if (!$auth->hasRole('admin') && !$auth->hasRole('superadmin')) {
    redirectWithMessage('dashboard.php', 'Insufficient permissions to access GraphSearch.', 'error');
}

$currentUser = $auth->getCurrentUser();

// Log GraphSearch access
$logger = new SecurityLogger();
$logger->logAdminAction(
    $currentUser['id'],
    'graphsearch_access',
    'Accessed GraphSearch application',
    getClientIP()
);

// Get flash message
$flash = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GraphSearch - <?php echo getConfig('app', 'app_name'); ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        linkedin: {
                            blue: '#0077b5',
                            darkBlue: '#004471'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .admin-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 24px;
            color: white;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .license-key {
            font-family: 'Courier New', monospace;
            background: #f3f4f6;
            color: #1f2937;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #d1d5db;
            font-size: 14px;
            letter-spacing: 1px;
        }
        .dark .license-key {
            background: #374151;
            color: #f9fafb;
            border-color: #4b5563;
        }
        .status-active { color: #10b981; }
        .status-expired { color: #ef4444; }
        .status-suspended { color: #f59e0b; }
        .tier-trial { background: #fef3c7; color: #d97706; }
        .tier-basic { background: #dbeafe; color: #2563eb; }
        .tier-professional { background: #dcfce7; color: #16a34a; }
        .tier-enterprise { background: #f3e8ff; color: #9333ea; }
        .dark .tier-trial { background: #d97706; color: #fef3c7; }
        .dark .tier-basic { background: #2563eb; color: #dbeafe; }
        .dark .tier-professional { background: #16a34a; color: #dcfce7; }
        .dark .tier-enterprise { background: #9333ea; color: #f3e8ff; }

        .license-key-container {
            max-width: 200px;
        }

        .license-key-preview {
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .license-key-preview:hover {
            background-color: #e5e7eb !important;
            transform: scale(1.02);
        }

        .dark .license-key-preview:hover {
            background-color: #4b5563 !important;
        }

        .license-key-preview::after {
            content: "Click to copy";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            opacity: 0;
            transition: opacity 0.2s ease;
            pointer-events: none;
            white-space: nowrap;
        }

        .license-key-preview:hover::after {
            opacity: 1;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200">
    <!-- Admin Header -->
    <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-2xl text-linkedin-blue mr-3"></i>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Admin Dashboard</h1>
                    <span class="ml-3 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">ADMIN ONLY</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-user mr-1"></i>Admin User
                    </div>
                    <button id="toggleTheme" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="fas fa-sun hidden dark:inline"></i>
                    </button>
                    <a href="linkedin-search-app-v3.html" class="bg-linkedin-blue text-white px-4 py-2 rounded-lg hover:bg-linkedin-darkBlue transition-colors">
                        <i class="fas fa-external-link-alt mr-2"></i>View App
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Dashboard Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="admin-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-80">Total Licenses</p>
                        <p id="totalLicenses" class="text-3xl font-bold">0</p>
                    </div>
                    <i class="fas fa-key text-3xl opacity-60"></i>
                </div>
            </div>
            <div class="admin-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-80">Active Licenses</p>
                        <p id="activeLicenses" class="text-3xl font-bold">0</p>
                    </div>
                    <i class="fas fa-check-circle text-3xl opacity-60"></i>
                </div>
            </div>
            <div class="admin-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-80">Revenue (Monthly)</p>
                        <p id="monthlyRevenue" class="text-3xl font-bold">$0</p>
                    </div>
                    <i class="fas fa-dollar-sign text-3xl opacity-60"></i>
                </div>
            </div>
            <div class="admin-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-80">Expiring Soon</p>
                        <p id="expiringSoon" class="text-3xl font-bold">0</p>
                    </div>
                    <i class="fas fa-exclamation-triangle text-3xl opacity-60"></i>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- License Generator -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-xl font-semibold mb-6 flex items-center">
                        <i class="fas fa-plus-circle mr-2 text-green-500"></i>Generate New License
                    </h2>

                    <form id="licenseForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">License Type</label>
                            <select id="licenseType" class="w-full p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                                <option value="basic">Basic ($29/mo)</option>
                                <option value="professional">Professional ($79/mo)</option>
                                <option value="enterprise">Enterprise ($199/mo)</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">Customer Email</label>
                            <input type="email" id="customerEmail" placeholder="<EMAIL>" class="w-full p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">Customer Name</label>
                            <input type="text" id="customerName" placeholder="John Doe" class="w-full p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">Duration</label>
                            <select id="licenseDuration" class="w-full p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                                <option value="1">1 Month</option>
                                <option value="3">3 Months</option>
                                <option value="6">6 Months</option>
                                <option value="12" selected>12 Months</option>
                                <option value="24">24 Months</option>
                                <option value="36">36 Months</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">Max Devices</label>
                            <select id="maxDevices" class="w-full p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                                <option value="1">1 Device</option>
                                <option value="2">2 Devices</option>
                                <option value="3" selected>3 Devices</option>
                                <option value="5">5 Devices</option>
                                <option value="10">10 Devices</option>
                                <option value="25">25 Devices</option>
                                <option value="50">50 Devices</option>
                                <option value="-1">Unlimited Devices</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">Notes (Optional)</label>
                            <textarea id="licenseNotes" rows="3" placeholder="Internal notes..." class="w-full p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600"></textarea>
                        </div>

                        <button type="submit" id="generateBtn" class="w-full bg-green-500 text-white py-3 rounded-lg hover:bg-green-600 transition-colors font-medium">
                            <i class="fas fa-key mr-2"></i>Generate License Key
                        </button>

                        <button type="button" id="generateTestBtn" onclick="generateTestLicense()" class="w-full mt-2 bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors font-medium">
                            <i class="fas fa-flask mr-2"></i>Generate Test License
                        </button>

                        <button type="button" id="cancelEditBtn" onclick="cancelEdit()" class="w-full mt-2 bg-gray-500 text-white py-2 rounded-lg hover:bg-gray-600 transition-colors font-medium" style="display: none;">
                            <i class="fas fa-times mr-2"></i>Cancel Edit
                        </button>
                    </form>

                    <!-- Generated License Display -->
                    <div id="generatedLicense" class="hidden mt-6 p-4 bg-green-50 dark:bg-green-900 rounded-lg border border-green-200 dark:border-green-700">
                        <h3 class="font-semibold text-green-800 dark:text-green-200 mb-2">License Generated Successfully!</h3>
                        <div class="space-y-2">
                            <div>
                                <label class="text-sm font-medium text-green-700 dark:text-green-300">License Key:</label>
                                <div class="license-key mt-1" id="newLicenseKey">XXXX-XXXX-XXXX-XXXX</div>
                            </div>
                            <div class="flex gap-2 mt-3">
                                <button id="copyLicenseKey" class="flex-1 bg-green-500 text-white py-2 px-3 rounded text-sm hover:bg-green-600">
                                    <i class="fas fa-copy mr-1"></i>Copy Key
                                </button>
                                <button id="emailLicenseKey" class="flex-1 bg-blue-500 text-white py-2 px-3 rounded text-sm hover:bg-blue-600">
                                    <i class="fas fa-envelope mr-1"></i>Email Customer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- License Management -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-semibold flex items-center">
                            <i class="fas fa-list mr-2 text-blue-500"></i>License Management
                        </h2>
                        <div class="flex gap-2">
                            <button id="exportLicenses" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                                <i class="fas fa-download mr-2"></i>Export
                            </button>
                            <button id="refreshLicenses" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                <i class="fas fa-sync-alt mr-2"></i>Refresh
                            </button>
                        </div>
                    </div>

                    <!-- Search and Filter -->
                    <div class="flex gap-4 mb-6">
                        <div class="flex-1">
                            <input type="text" id="searchLicenses" placeholder="Search by email, name, or license key..." class="w-full p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                        </div>
                        <select id="filterStatus" class="p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="expired">Expired</option>
                            <option value="suspended">Suspended</option>
                        </select>
                        <select id="filterType" class="p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                            <option value="">All Types</option>
                            <option value="basic">Basic</option>
                            <option value="professional">Professional</option>
                            <option value="enterprise">Enterprise</option>
                        </select>
                    </div>

                    <!-- License Table -->
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-200 dark:border-gray-700">
                                    <th class="text-left py-3 px-2 font-medium text-gray-600 dark:text-gray-400">Customer</th>
                                    <th class="text-left py-3 px-2 font-medium text-gray-600 dark:text-gray-400">License Key</th>
                                    <th class="text-left py-3 px-2 font-medium text-gray-600 dark:text-gray-400">Type</th>
                                    <th class="text-left py-3 px-2 font-medium text-gray-600 dark:text-gray-400">Status</th>
                                    <th class="text-left py-3 px-2 font-medium text-gray-600 dark:text-gray-400">Expires</th>
                                    <th class="text-left py-3 px-2 font-medium text-gray-600 dark:text-gray-400">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="licensesTableBody">
                                <tr>
                                    <td colspan="6" class="text-center py-8 text-gray-500 dark:text-gray-400">
                                        <i class="fas fa-key text-4xl mb-2"></i>
                                        <div>No licenses generated yet</div>
                                        <div class="text-sm">Generate your first license above</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Cryptographic license functions (same as main app)
            const LICENSE_SECRET = 'ELITEAPPSUITE2024LINKEDIN';

            function encryptLicenseData(data, secret) {
                const jsonStr = JSON.stringify(data);
                let encrypted = '';
                for (let i = 0; i < jsonStr.length; i++) {
                    const charCode = jsonStr.charCodeAt(i) ^ secret.charCodeAt(i % secret.length);
                    encrypted += String.fromCharCode(charCode);
                }
                // Use hex encoding instead of base64 for better compatibility
                return Array.from(encrypted).map(char =>
                    char.charCodeAt(0).toString(16).padStart(2, '0')
                ).join('').toUpperCase();
            }

            function generateCryptographicLicenseKey(licenseData) {
                const encryptedData = encryptLicenseData(licenseData, LICENSE_SECRET);

                // Format as readable license key: XXXX-XXXX-XXXX-XXXX-XXXX-XXXX
                const chunks = [];
                for (let i = 0; i < encryptedData.length; i += 4) {
                    chunks.push(encryptedData.substr(i, 4));
                }

                return chunks.join('-');
            }

            function decryptLicenseData(encryptedData, secret) {
                try {
                    // Convert hex back to string
                    let encrypted = '';
                    for (let i = 0; i < encryptedData.length; i += 2) {
                        const hex = encryptedData.substr(i, 2);
                        encrypted += String.fromCharCode(parseInt(hex, 16));
                    }

                    let decrypted = '';
                    for (let i = 0; i < encrypted.length; i++) {
                        const charCode = encrypted.charCodeAt(i) ^ secret.charCodeAt(i % secret.length);
                        decrypted += String.fromCharCode(charCode);
                    }
                    return JSON.parse(decrypted);
                } catch (error) {
                    console.error('Decryption error:', error);
                    return null;
                }
            }

            // Global variables
            let licenses = JSON.parse(localStorage.getItem('adminLicenses') || '[]');
            let licenseCounter = parseInt(localStorage.getItem('licenseCounter') || '1000');

            // Initialize
            updateDashboardStats();
            renderLicensesTable();

            // Theme toggle
            $('#toggleTheme').click(function() {
                $('html').toggleClass('dark');
                localStorage.setItem('adminTheme', $('html').hasClass('dark') ? 'dark' : 'light');
            });

            // Load saved theme
            if (localStorage.getItem('adminTheme') === 'dark') {
                $('html').addClass('dark');
            }

            // License generation form
            $('#licenseForm').on('submit', function(e) {
                e.preventDefault();
                generateLicense();
            });

            // Test license generation (handled by onclick attribute)

            // Copy license key
            $('#copyLicenseKey').click(function() {
                const licenseKey = $('#newLicenseKey').text();
                navigator.clipboard.writeText(licenseKey).then(function() {
                    showSuccess('License key copied to clipboard!');
                });
            });

            // Email license key
            $('#emailLicenseKey').click(function() {
                emailLicenseToCustomer();
            });

            // Search and filter
            $('#searchLicenses, #filterStatus, #filterType').on('input change', function() {
                renderLicensesTable();
            });

            // Export licenses
            $('#exportLicenses').click(function() {
                exportLicensesToExcel();
            });

            // Refresh licenses
            $('#refreshLicenses').click(function() {
                renderLicensesTable();
                updateDashboardStats();
                showSuccess('Licenses refreshed!');
            });

            // Generate License Function
            function generateLicense() {
                const type = $('#licenseType').val();
                const email = $('#customerEmail').val().trim();
                const name = $('#customerName').val().trim();
                const duration = parseInt($('#licenseDuration').val());
                const maxDevices = parseInt($('#maxDevices').val());
                const notes = $('#licenseNotes').val().trim();

                // Validation
                if (!email || !name) {
                    showError('Please fill in customer email and name');
                    return;
                }

                if (!isValidEmail(email)) {
                    showError('Please enter a valid email address');
                    return;
                }

                // Check if we're editing an existing license
                if (editingLicenseId) {
                    updateExistingLicense(editingLicenseId, type, email, name, duration, maxDevices, notes);
                    return;
                }

                // Check for duplicate email (only for new licenses)
                const existingLicense = licenses.find(license =>
                    license.customerEmail.toLowerCase() === email.toLowerCase() &&
                    license.status !== 'deleted'
                );

                if (existingLicense) {
                    showError(`A license already exists for email: ${email}. Please use a different email address or manage the existing license.`);
                    return;
                }

                // Calculate expiry date
                const expiryDate = new Date();
                expiryDate.setMonth(expiryDate.getMonth() + duration);

                // Generate cryptographic license key
                const licenseKey = generateLicenseKey(type, email, expiryDate);

                // Create license object
                const license = {
                    id: Date.now(),
                    key: licenseKey,
                    type: type,
                    customerEmail: email,
                    customerName: name,
                    status: 'active',
                    createdDate: new Date(),
                    expiryDate: expiryDate,
                    duration: duration,
                    maxDevices: maxDevices,
                    notes: notes,
                    usage: {
                        totalSearches: 0,
                        lastUsed: null
                    }
                };

                // Add to licenses array
                licenses.push(license);
                saveLicenses();

                // Update UI
                updateDashboardStats();
                renderLicensesTable();

                // Show generated license
                $('#newLicenseKey').text(licenseKey);
                $('#generatedLicense').removeClass('hidden');

                // Reset form
                $('#licenseForm')[0].reset();

                showSuccess('License generated successfully!');
            }

            function generateTestLicense() {
                // Pre-fill form with test data
                $('#licenseType').val('professional');
                $('#customerEmail').val('<EMAIL>');
                $('#customerName').val('Test User');
                $('#licenseDuration').val('12');
                $('#maxDevices').val('3');
                $('#licenseNotes').val('Test license for debugging');

                // Generate the license
                generateLicense();

                showSuccess('Test license generated! Use email: <EMAIL>');
            }

            function generateLicenseKey(type, email, expiryDate) {
                // Create license data object
                const licenseData = {
                    type: type,
                    email: email,
                    expiryDate: expiryDate.toISOString(),
                    generatedDate: new Date().toISOString(),
                    version: '1.0'
                };

                // Generate cryptographic license key
                return generateCryptographicLicenseKey(licenseData);
            }



            function testLicenseGeneration() {
                console.log('Testing license generation...');

                // Test data
                const testData = {
                    type: 'professional',
                    email: '<EMAIL>',
                    expiryDate: new Date('2025-12-31').toISOString(),
                    generatedDate: new Date().toISOString(),
                    version: '1.0'
                };

                console.log('Original data:', testData);

                // Generate license key
                const licenseKey = generateCryptographicLicenseKey(testData);
                console.log('Generated license key:', licenseKey);

                // Test decryption (add decryption function here)
                const decryptedData = decryptLicenseData(licenseKey.replace(/-/g, ''), LICENSE_SECRET);
                console.log('Decrypted data:', decryptedData);

                if (JSON.stringify(testData) === JSON.stringify(decryptedData)) {
                    console.log('✅ License generation test PASSED');
                } else {
                    console.log('❌ License generation test FAILED');
                }
            }



            function generateRandomSegment() {
                const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                let result = '';
                for (let i = 0; i < 4; i++) {
                    result += chars.charAt(Math.floor(Math.random() * chars.length));
                }
                return result;
            }

            function updateDashboardStats() {
                const total = licenses.length;
                const active = licenses.filter(l => l.status === 'active' && new Date() < new Date(l.expiryDate)).length;
                const expiringSoon = licenses.filter(l => {
                    const daysUntilExpiry = Math.ceil((new Date(l.expiryDate) - new Date()) / (1000 * 60 * 60 * 24));
                    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
                }).length;

                // Calculate monthly revenue
                const monthlyRevenue = licenses.filter(l => l.status === 'active').reduce((sum, license) => {
                    const prices = { basic: 29, professional: 79, enterprise: 199 };
                    return sum + (prices[license.type] || 0);
                }, 0);

                $('#totalLicenses').text(total);
                $('#activeLicenses').text(active);
                $('#monthlyRevenue').text('$' + monthlyRevenue.toLocaleString());
                $('#expiringSoon').text(expiringSoon);
            }

            function renderLicensesTable() {
                const searchTerm = $('#searchLicenses').val().toLowerCase();
                const statusFilter = $('#filterStatus').val();
                const typeFilter = $('#filterType').val();

                let filteredLicenses = licenses.filter(license => {
                    const matchesSearch = !searchTerm ||
                        license.customerEmail.toLowerCase().includes(searchTerm) ||
                        license.customerName.toLowerCase().includes(searchTerm) ||
                        license.key.toLowerCase().includes(searchTerm);

                    const matchesStatus = !statusFilter || license.status === statusFilter;
                    const matchesType = !typeFilter || license.type === typeFilter;

                    return matchesSearch && matchesStatus && matchesType;
                });

                const tbody = $('#licensesTableBody');

                if (filteredLicenses.length === 0) {
                    tbody.html(`
                        <tr>
                            <td colspan="6" class="text-center py-8 text-gray-500 dark:text-gray-400">
                                <i class="fas fa-search text-4xl mb-2"></i>
                                <div>No licenses found</div>
                                <div class="text-sm">Try adjusting your search or filters</div>
                            </td>
                        </tr>
                    `);
                    return;
                }

                const rows = filteredLicenses.map(license => {
                    const isExpired = new Date() > new Date(license.expiryDate);
                    const status = isExpired ? 'expired' : license.status;
                    const daysUntilExpiry = Math.ceil((new Date(license.expiryDate) - new Date()) / (1000 * 60 * 60 * 24));

                    return `
                        <tr class="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="py-3 px-2">
                                <div class="font-medium">${license.customerName}</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">${license.customerEmail}</div>
                            </td>
                            <td class="py-3 px-2">
                                <div class="license-key-container">
                                    <div class="license-key-preview text-xs font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded border" onclick="copyLicenseKey('${license.key}')">
                                        ${license.key.substring(0, 20)}...
                                    </div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        ${license.key.split('-').length} segments
                                    </div>
                                </div>
                            </td>
                            <td class="py-3 px-2">
                                <span class="px-2 py-1 rounded-full text-xs font-medium tier-${license.type}">
                                    ${license.type.charAt(0).toUpperCase() + license.type.slice(1)}
                                </span>
                            </td>
                            <td class="py-3 px-2">
                                <span class="status-${status}">
                                    <i class="fas fa-circle text-xs mr-1"></i>
                                    ${status.charAt(0).toUpperCase() + status.slice(1)}
                                </span>
                            </td>
                            <td class="py-3 px-2">
                                <div class="text-sm">${new Date(license.expiryDate).toLocaleDateString()}</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    ${daysUntilExpiry > 0 ? `${daysUntilExpiry} days left` : `Expired ${Math.abs(daysUntilExpiry)} days ago`}
                                </div>
                                <div class="text-xs text-blue-600 dark:text-blue-400 cursor-pointer hover:underline" onclick="showDeviceDetails('${license.key}')">
                                    Devices: ${getDeviceCount(license.key)}/${(license.maxDevices === -1) ? '∞' : (license.maxDevices || 3)}
                                    <i class="fas fa-eye ml-1"></i>
                                </div>
                            </td>
                            <td class="py-3 px-2">
                                <div class="flex gap-1">
                                    <button onclick="editLicense('${license.id}')" class="text-blue-500 hover:text-blue-700 p-1" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button onclick="suspendLicense('${license.id}')" class="text-yellow-500 hover:text-yellow-700 p-1" title="Suspend">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                    <button onclick="deleteLicense('${license.id}')" class="text-red-500 hover:text-red-700 p-1" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <button onclick="copyLicenseKey('${license.key}')" class="text-green-500 hover:text-green-700 p-1" title="Copy Key">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                }).join('');

                tbody.html(rows);
            }

            // Device Tracking Functions
            function getDeviceCount(licenseKey) {
                const deviceTracking = JSON.parse(localStorage.getItem('deviceTracking') || '{}');
                return deviceTracking[licenseKey] ? deviceTracking[licenseKey].devices.length : 0;
            }

            function getDeviceDetails(licenseKey) {
                const deviceTracking = JSON.parse(localStorage.getItem('deviceTracking') || '{}');
                return deviceTracking[licenseKey] ? deviceTracking[licenseKey].devices : [];
            }

            function showDeviceDetails(licenseKey) {
                const devices = getDeviceDetails(licenseKey);
                const license = licenses.find(l => l.key === licenseKey);

                if (!license) {
                    showError('License not found');
                    return;
                }

                let deviceListHtml = '';
                if (devices.length === 0) {
                    deviceListHtml = '<p class="text-gray-500 text-center py-4">No devices activated yet</p>';
                } else {
                    devices.forEach((device, index) => {
                        const activatedDate = new Date(device.activatedAt).toLocaleDateString();
                        const lastUsedDate = new Date(device.lastUsed).toLocaleDateString();
                        const browserIcon = getBrowserIcon(device.browserName);

                        deviceListHtml += `
                            <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded border mb-2">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="text-2xl">${browserIcon}</div>
                                        <div>
                                            <div class="font-medium">${device.browserName} ${device.browserVersion}</div>
                                            <div class="text-sm text-gray-500">Device ID: ${device.deviceId}</div>
                                            <div class="text-xs text-gray-400">Platform: ${device.platform}</div>
                                        </div>
                                    </div>
                                    <div class="text-right text-sm">
                                        <div class="text-gray-600 dark:text-gray-400">Activated: ${activatedDate}</div>
                                        <div class="text-gray-600 dark:text-gray-400">Last Used: ${lastUsedDate}</div>
                                        <div class="text-blue-600 dark:text-blue-400">Uses: ${device.activationCount || 1}</div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                }

                // Show modal with device details
                const modalHtml = `
                    <div id="deviceModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold">Device Details - ${license.customerName}</h3>
                                <button onclick="closeDeviceModal()" class="text-gray-500 hover:text-gray-700">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="mb-4">
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    License: ${license.type.charAt(0).toUpperCase() + license.type.slice(1)} |
                                    Max Devices: ${license.maxDevices === -1 ? 'Unlimited' : license.maxDevices} |
                                    Active: ${devices.length}
                                </p>
                            </div>
                            <div class="space-y-2">
                                ${deviceListHtml}
                            </div>
                        </div>
                    </div>
                `;

                $('body').append(modalHtml);
            }

            function getBrowserIcon(browserName) {
                switch (browserName.toLowerCase()) {
                    case 'chrome': return '🌐';
                    case 'firefox': return '🦊';
                    case 'safari': return '🧭';
                    case 'edge': return '🔷';
                    default: return '💻';
                }
            }

            function closeDeviceModal() {
                $('#deviceModal').remove();
            }

            // Form Mode Management
            function updateFormMode(isEditing) {
                const generateBtn = $('#generateBtn');
                const testBtn = $('#generateTestBtn');
                const cancelBtn = $('#cancelEditBtn');

                if (isEditing) {
                    generateBtn.html('<i class="fas fa-save mr-2"></i>Update License');
                    generateBtn.removeClass('bg-green-600 hover:bg-green-700').addClass('bg-blue-600 hover:bg-blue-700');
                    testBtn.hide();
                    cancelBtn.show();
                } else {
                    generateBtn.html('<i class="fas fa-key mr-2"></i>Generate License Key');
                    generateBtn.removeClass('bg-blue-600 hover:bg-blue-700').addClass('bg-green-600 hover:bg-green-700');
                    testBtn.show();
                    cancelBtn.hide();
                    editingLicenseId = null;
                }
            }

            function updateExistingLicense(licenseId, type, email, name, duration, maxDevices, notes) {
                const licenseIndex = licenses.findIndex(l => l.id == licenseId);
                if (licenseIndex === -1) {
                    showError('License not found');
                    return;
                }

                const license = licenses[licenseIndex];

                // Check for duplicate email (excluding current license)
                const existingLicense = licenses.find(l =>
                    l.id != licenseId &&
                    l.customerEmail.toLowerCase() === email.toLowerCase() &&
                    l.status !== 'deleted'
                );

                if (existingLicense) {
                    showError(`Another license already exists for email: ${email}. Please use a different email address.`);
                    return;
                }

                // Calculate new expiry date based on current date + duration
                const expiryDate = new Date();
                expiryDate.setMonth(expiryDate.getMonth() + duration);

                // Update license properties
                license.type = type;
                license.customerEmail = email;
                license.customerName = name;
                license.duration = duration;
                license.maxDevices = maxDevices;
                license.notes = notes;
                license.expiryDate = expiryDate;
                license.updatedDate = new Date();

                // Save changes
                saveLicenses();

                // Update UI
                updateDashboardStats();
                renderLicensesTable();

                // Reset form mode
                updateFormMode(false);

                // Clear form
                $('#licenseForm')[0].reset();
                $('#licenseType').val('basic');
                $('#licenseDuration').val('12');
                $('#maxDevices').val('3');

                showSuccess(`License for ${name} updated successfully!`);
            }

            function cancelEdit() {
                // Clear form
                $('#licenseForm')[0].reset();
                $('#licenseType').val('basic');
                $('#licenseDuration').val('12');
                $('#maxDevices').val('3');

                // Reset form mode
                updateFormMode(false);

                showSuccess('Edit cancelled. Form reset for new license generation.');
            }

            // Utility Functions
            function saveLicenses() {
                localStorage.setItem('adminLicenses', JSON.stringify(licenses));
            }

            function isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            function emailLicenseToCustomer() {
                const licenseKey = $('#newLicenseKey').text();
                const email = $('#customerEmail').val();
                const name = $('#customerName').val();
                const type = $('#licenseType').val();

                const subject = encodeURIComponent('Your LinkedIn Search App License Key');
                const body = encodeURIComponent(`Dear ${name},

Thank you for purchasing the ${type.charAt(0).toUpperCase() + type.slice(1)} license for LinkedIn Search App!

Your license key is: ${licenseKey}

To activate your license:
1. Open the LinkedIn Search App
2. Click the "License" button in the top right
3. Enter your license key in the "Enter License Key" field
4. Click "Validate"

Your license includes all the features of the ${type} tier. If you have any questions, please don't hesitate to contact our support team.

Best regards,
LinkedIn Search App Team`);

                window.open(`mailto:${email}?subject=${subject}&body=${body}`, '_blank');
                showSuccess('Opening email client to send license to customer...');
            }

            function exportLicensesToExcel() {
                if (licenses.length === 0) {
                    showError('No licenses to export');
                    return;
                }

                const exportData = licenses.map(license => ({
                    'License Key': license.key,
                    'Customer Name': license.customerName,
                    'Customer Email': license.customerEmail,
                    'License Type': license.type.charAt(0).toUpperCase() + license.type.slice(1),
                    'Status': license.status,
                    'Created Date': new Date(license.createdDate).toLocaleDateString(),
                    'Expiry Date': new Date(license.expiryDate).toLocaleDateString(),
                    'Duration (Months)': license.duration,
                    'Max Devices': license.maxDevices === -1 ? 'Unlimited' : license.maxDevices,
                    'Active Devices': getDeviceCount(license.key),
                    'Notes': license.notes || '',
                    'Total Searches': license.usage.totalSearches,
                    'Last Used': license.usage.lastUsed ? new Date(license.usage.lastUsed).toLocaleDateString() : 'Never'
                }));

                const ws = XLSX.utils.json_to_sheet(exportData);
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, 'Licenses');

                const filename = `linkedin-search-licenses-${new Date().toISOString().split('T')[0]}.xlsx`;
                XLSX.writeFile(wb, filename);

                showSuccess('Licenses exported successfully!');
            }

            function showSuccess(message) {
                showNotification(message, 'success');
            }

            function showError(message) {
                showNotification(message, 'error');
            }

            function showNotification(message, type) {
                const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
                const notification = $(`
                    <div class="fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300">
                        <i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'} mr-2"></i>
                        ${message}
                    </div>
                `);

                $('body').append(notification);

                setTimeout(() => {
                    notification.removeClass('translate-x-full');
                }, 100);

                setTimeout(() => {
                    notification.addClass('translate-x-full');
                    setTimeout(() => notification.remove(), 300);
                }, 3000);
            }

            // Global variable to track editing state
            let editingLicenseId = null;

            // Global functions for table actions
            window.editLicense = function(licenseId) {
                const license = licenses.find(l => l.id == licenseId);
                if (!license) return;

                // Set editing mode
                editingLicenseId = licenseId;

                // Fill form with license data for editing
                $('#licenseType').val(license.type);
                $('#customerEmail').val(license.customerEmail);
                $('#customerName').val(license.customerName);
                $('#licenseDuration').val(license.duration);
                $('#maxDevices').val(license.maxDevices || 3);
                $('#licenseNotes').val(license.notes);

                // Change button text and functionality
                updateFormMode(true);

                // Scroll to form
                $('html, body').animate({
                    scrollTop: $('#licenseForm').offset().top - 100
                }, 500);

                showSuccess('License data loaded for editing. Click "Update License" to save changes.');
            };

            window.suspendLicense = function(licenseId) {
                const license = licenses.find(l => l.id == licenseId);
                if (!license) return;

                if (confirm(`Are you sure you want to suspend the license for ${license.customerName}?`)) {
                    license.status = license.status === 'suspended' ? 'active' : 'suspended';
                    saveLicenses();
                    renderLicensesTable();
                    updateDashboardStats();

                    const action = license.status === 'suspended' ? 'suspended' : 'reactivated';
                    showSuccess(`License ${action} successfully!`);
                }
            };

            window.deleteLicense = function(licenseId) {
                const license = licenses.find(l => l.id == licenseId);
                if (!license) return;

                if (confirm(`Are you sure you want to delete the license for ${license.customerName}? This action cannot be undone.`)) {
                    licenses = licenses.filter(l => l.id != licenseId);
                    saveLicenses();
                    renderLicensesTable();
                    updateDashboardStats();
                    showSuccess('License deleted successfully!');
                }
            };

            window.copyLicenseKey = function(licenseKey) {
                navigator.clipboard.writeText(licenseKey).then(function() {
                    showSuccess('License key copied to clipboard!');
                });
            };
        });
    </script>
</body>
</html>
