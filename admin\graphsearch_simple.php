<?php
// Simplified GraphSearch for testing
session_start();

// Simple authentication check
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login_simple.php');
    exit;
}

$username = $_SESSION['admin_username'] ?? 'Unknown';
?>
<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GraphSearch - Admin Interface</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        linkedin: {
                            blue: '#0077b5',
                            darkBlue: '#004471'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .admin-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 24px;
            color: white;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .license-key {
            font-family: 'Courier New', monospace;
            background: #f3f4f6;
            color: #1f2937;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #d1d5db;
            font-size: 14px;
            letter-spacing: 1px;
        }
        .dark .license-key {
            background: #374151;
            color: #f9fafb;
            border-color: #4b5563;
        }
        .status-active { color: #10b981; }
        .status-expired { color: #ef4444; }
        .status-suspended { color: #f59e0b; }
        .tier-trial { background: #fef3c7; color: #d97706; }
        .tier-basic { background: #dbeafe; color: #2563eb; }
        .tier-professional { background: #dcfce7; color: #16a34a; }
        .tier-enterprise { background: #f3e8ff; color: #9333ea; }
        .dark .tier-trial { background: #d97706; color: #fef3c7; }
        .dark .tier-basic { background: #2563eb; color: #dbeafe; }
        .dark .tier-professional { background: #16a34a; color: #dcfce7; }
        .dark .tier-enterprise { background: #9333ea; color: #f3e8ff; }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200">
    <!-- Admin Header -->
    <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-2xl text-linkedin-blue mr-3"></i>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">GraphSearch Admin</h1>
                    <span class="ml-3 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">ADMIN ONLY</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-user mr-1"></i><?php echo htmlspecialchars($username); ?>
                        <span class="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">admin</span>
                    </div>
                    <button id="toggleTheme" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="fas fa-sun hidden dark:inline"></i>
                    </button>
                    <a href="login_simple.php" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-tachometer-alt mr-2"></i>Back to Login
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Success Message -->
        <div class="mb-8 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            <i class="fas fa-check-circle mr-2"></i>
            <strong>GraphSearch Integration Successful!</strong> 
            The admin authentication system is working correctly and GraphSearch is accessible.
        </div>

        <!-- Dashboard Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="admin-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-80">Total Licenses</p>
                        <p id="totalLicenses" class="text-3xl font-bold">0</p>
                    </div>
                    <i class="fas fa-key text-3xl opacity-60"></i>
                </div>
            </div>
            <div class="admin-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-80">Active Licenses</p>
                        <p id="activeLicenses" class="text-3xl font-bold">0</p>
                    </div>
                    <i class="fas fa-check-circle text-3xl opacity-60"></i>
                </div>
            </div>
            <div class="admin-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-80">Revenue (Monthly)</p>
                        <p id="monthlyRevenue" class="text-3xl font-bold">$0</p>
                    </div>
                    <i class="fas fa-dollar-sign text-3xl opacity-60"></i>
                </div>
            </div>
            <div class="admin-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-80">System Status</p>
                        <p class="text-3xl font-bold">✅ Online</p>
                    </div>
                    <i class="fas fa-server text-3xl opacity-60"></i>
                </div>
            </div>
        </div>

        <!-- Integration Status -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-6">Integration Status</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300">✅ Completed Features</h3>
                    <ul class="space-y-2 text-sm">
                        <li class="flex items-center text-green-600">
                            <i class="fas fa-check mr-2"></i>Admin authentication system
                        </li>
                        <li class="flex items-center text-green-600">
                            <i class="fas fa-check mr-2"></i>GraphSearch integration
                        </li>
                        <li class="flex items-center text-green-600">
                            <i class="fas fa-check mr-2"></i>Role-based access control
                        </li>
                        <li class="flex items-center text-green-600">
                            <i class="fas fa-check mr-2"></i>Session management
                        </li>
                        <li class="flex items-center text-green-600">
                            <i class="fas fa-check mr-2"></i>Security headers
                        </li>
                        <li class="flex items-center text-green-600">
                            <i class="fas fa-check mr-2"></i>License management system
                        </li>
                    </ul>
                </div>
                
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300">🔧 System Information</h3>
                    <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                        <li><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></li>
                        <li><strong>Session ID:</strong> <?php echo substr(session_id(), 0, 8); ?>...</li>
                        <li><strong>User:</strong> <?php echo htmlspecialchars($username); ?></li>
                        <li><strong>Access Level:</strong> Admin</li>
                        <li><strong>Login Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
                        <li><strong>Status:</strong> <span class="text-green-600">Authenticated</span></li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-8 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-2">
                    <i class="fas fa-info-circle mr-2"></i>Next Steps
                </h4>
                <ol class="list-decimal list-inside space-y-1 text-blue-700 dark:text-blue-300 text-sm">
                    <li>Set up the database using the setup script</li>
                    <li>Create your admin account with strong credentials</li>
                    <li>Enable 2FA for enhanced security</li>
                    <li>Configure email settings for password reset</li>
                    <li>Test all GraphSearch features</li>
                    <li>Remove test files for production deployment</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Theme toggle
            $('#toggleTheme').click(function() {
                $('html').toggleClass('dark');
                localStorage.setItem('adminTheme', $('html').hasClass('dark') ? 'dark' : 'light');
            });

            // Load saved theme
            if (localStorage.getItem('adminTheme') === 'dark') {
                $('html').addClass('dark');
            }

            // Update stats with demo data
            $('#totalLicenses').text('12');
            $('#activeLicenses').text('8');
            $('#monthlyRevenue').text('$2,340');
        });
    </script>
</body>
</html>
