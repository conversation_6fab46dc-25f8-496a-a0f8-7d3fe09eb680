<?php
/**
 * Modern Admin Dashboard
 * 
 * Beautiful, responsive dashboard with enhanced UX
 */

session_start();

// Check authentication
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login_modern.php');
    exit;
}

$username = $_SESSION['admin_username'] ?? 'Unknown';
$email = $_SESSION['admin_email'] ?? '<EMAIL>';
$role = $_SESSION['admin_role'] ?? 'user';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - GraphDB Admin System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'bounce-in': 'bounceIn 0.8s ease-out'
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-shield-alt text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gray-900">GraphDB Admin</h1>
                            <p class="text-xs text-gray-500">Management Dashboard</p>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- Notifications -->
                    <button class="relative p-2 text-gray-400 hover:text-gray-600 transition-colors">
                        <i class="fas fa-bell text-lg"></i>
                        <span class="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
                    </button>
                    
                    <!-- User Menu -->
                    <div class="flex items-center space-x-3">
                        <div class="text-right">
                            <p class="text-sm font-medium text-gray-700"><?php echo htmlspecialchars($username); ?></p>
                            <p class="text-xs text-gray-500"><?php echo htmlspecialchars($role); ?></p>
                        </div>
                        <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-purple-600 rounded-full flex items-center justify-center">
                            <span class="text-white font-semibold text-sm"><?php echo strtoupper(substr($username, 0, 1)); ?></span>
                        </div>
                    </div>
                    
                    <!-- Logout -->
                    <a href="login_modern.php" class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                        <i class="fas fa-sign-out-alt mr-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Welcome Section -->
        <div class="mb-8 animate-fade-in">
            <h2 class="text-3xl font-bold text-gray-900 mb-2">
                Welcome back, <?php echo htmlspecialchars($username); ?>! 👋
            </h2>
            <p class="text-gray-600">
                Here's what's happening with your GraphDB system today.
            </p>
        </div>

        <!-- Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Users -->
            <div class="stat-card bg-white rounded-xl shadow-sm p-6 transition-all duration-300 animate-bounce-in">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Users</p>
                        <p class="text-2xl font-bold text-gray-900">1,234</p>
                        <p class="text-xs text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i>12% from last month
                        </p>
                    </div>
                </div>
            </div>

            <!-- Active Sessions -->
            <div class="stat-card bg-white rounded-xl shadow-sm p-6 transition-all duration-300 animate-bounce-in" style="animation-delay: 0.1s;">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Sessions</p>
                        <p class="text-2xl font-bold text-gray-900">89</p>
                        <p class="text-xs text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i>8% from yesterday
                        </p>
                    </div>
                </div>
            </div>

            <!-- Database Size -->
            <div class="stat-card bg-white rounded-xl shadow-sm p-6 transition-all duration-300 animate-bounce-in" style="animation-delay: 0.2s;">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-database text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Database Size</p>
                        <p class="text-2xl font-bold text-gray-900">2.4 GB</p>
                        <p class="text-xs text-blue-600">
                            <i class="fas fa-info-circle mr-1"></i>Healthy
                        </p>
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="stat-card bg-white rounded-xl shadow-sm p-6 transition-all duration-300 animate-bounce-in" style="animation-delay: 0.3s;">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-check-circle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">System Status</p>
                        <p class="text-2xl font-bold text-green-600">Online</p>
                        <p class="text-xs text-gray-500">
                            <i class="fas fa-clock mr-1"></i>99.9% uptime
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Quick Actions -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl shadow-sm p-6 animate-slide-up">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h3>
                    <div class="space-y-4">
                        <a href="graphsearch_working.php" class="block w-full p-4 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:from-purple-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105">
                            <div class="flex items-center">
                                <i class="fas fa-search text-xl mr-3"></i>
                                <div>
                                    <p class="font-semibold">Launch GraphSearch</p>
                                    <p class="text-sm opacity-90">Manage licenses and users</p>
                                </div>
                            </div>
                        </a>

                        <a href="#" onclick="open2FAModal()" class="block w-full p-4 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-300 transform hover:scale-105">
                            <div class="flex items-center">
                                <i class="fas fa-shield-alt text-xl mr-3"></i>
                                <div>
                                    <p class="font-semibold">Setup 2FA</p>
                                    <p class="text-sm opacity-90">Secure your account</p>
                                </div>
                            </div>
                        </a>

                    </div>
                </div>
            </div>

            <!-- Activity Chart -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-sm p-6 animate-slide-up" style="animation-delay: 0.2s;">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Activity Overview</h3>
                        <select class="text-sm border border-gray-300 rounded-lg px-3 py-1">
                            <option>Last 7 days</option>
                            <option>Last 30 days</option>
                            <option>Last 90 days</option>
                        </select>
                    </div>
                    <div class="h-64">
                        <canvas id="activityChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="mt-8 bg-white rounded-xl shadow-sm p-6 animate-slide-up" style="animation-delay: 0.4s;">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Recent Activity</h3>
            <div class="space-y-4">
                <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-user-plus text-green-600"></i>
                    </div>
                    <div class="flex-1">
                        <p class="font-medium text-gray-900">New user registered</p>
                        <p class="text-sm text-gray-500"><EMAIL> joined the system</p>
                    </div>
                    <span class="text-sm text-gray-400">2 minutes ago</span>
                </div>
                
                <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-key text-blue-600"></i>
                    </div>
                    <div class="flex-1">
                        <p class="font-medium text-gray-900">License generated</p>
                        <p class="text-sm text-gray-500">Professional license created for client</p>
                    </div>
                    <span class="text-sm text-gray-400">15 minutes ago</span>
                </div>
                
                <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-database text-purple-600"></i>
                    </div>
                    <div class="flex-1">
                        <p class="font-medium text-gray-900">Database backup completed</p>
                        <p class="text-sm text-gray-500">Automatic backup finished successfully</p>
                    </div>
                    <span class="text-sm text-gray-400">1 hour ago</span>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Initialize activity chart
        const ctx = document.getElementById('activityChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [{
                    label: 'Active Users',
                    data: [65, 78, 90, 81, 56, 85, 92],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'New Registrations',
                    data: [28, 35, 42, 38, 25, 40, 45],
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                }
            }
        });

        // Add real-time clock
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            document.title = `Dashboard - ${timeString} - GraphDB Admin`;
        }
        
        setInterval(updateClock, 1000);
        updateClock();

        // 2FA Modal Functions
        function open2FAModal() {
            document.getElementById('twoFAModal').classList.remove('hidden');
            generate2FASecret();
        }

        function close2FAModal() {
            document.getElementById('twoFAModal').classList.add('hidden');
        }

        function generate2FASecret() {
            fetch('2fa_setup.php?action=generate')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('qrCode').src = data.qr_url;
                        document.getElementById('secretKey').textContent = data.secret;
                        document.getElementById('secretInput').value = data.secret;
                    }
                })
                .catch(error => console.error('Error:', error));
        }

        function enable2FA() {
            const secret = document.getElementById('secretInput').value;
            const code = document.getElementById('verificationCode').value;

            if (!code || code.length !== 6) {
                alert('Please enter a valid 6-digit code');
                return;
            }

            fetch('2fa_setup.php?action=enable', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    secret: secret,
                    code: code
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('2FA enabled successfully!');
                    close2FAModal();
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while enabling 2FA');
            });
        }
    </script>

    <!-- 2FA Setup Modal -->
    <div id="twoFAModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Setup Two-Factor Authentication</h3>
                    <button onclick="close2FAModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="space-y-4">
                    <div class="text-center">
                        <p class="text-sm text-gray-600 mb-4">Scan this QR code with your authenticator app:</p>
                        <img id="qrCode" src="" alt="QR Code" class="mx-auto border rounded">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Or enter this secret key manually:</label>
                        <div class="bg-gray-100 p-2 rounded text-sm font-mono break-all" id="secretKey"></div>
                        <input type="hidden" id="secretInput">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Enter verification code:</label>
                        <input type="text" id="verificationCode" maxlength="6"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="123456">
                    </div>

                    <div class="flex space-x-3">
                        <button onclick="enable2FA()"
                                class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
                            Enable 2FA
                        </button>
                        <button onclick="close2FAModal()"
                                class="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
