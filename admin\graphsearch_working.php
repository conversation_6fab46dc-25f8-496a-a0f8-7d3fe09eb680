<?php
/**
 * Working GraphSearch Application
 * 
 * Integrated with database authentication
 */

session_start();

// Check authentication
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login_modern.php');
    exit;
}

$username = $_SESSION['admin_username'] ?? 'Unknown';
$role = $_SESSION['admin_role'] ?? 'user';
?>
<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GraphSearch - Admin Interface</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        linkedin: {
                            blue: '#0077b5',
                            darkBlue: '#004471'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .admin-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 24px;
            color: white;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .license-key {
            font-family: 'Courier New', monospace;
            background: #f3f4f6;
            color: #1f2937;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #d1d5db;
            font-size: 14px;
            letter-spacing: 1px;
        }
        .dark .license-key {
            background: #374151;
            color: #f9fafb;
            border-color: #4b5563;
        }
        .status-active { color: #10b981; }
        .status-expired { color: #ef4444; }
        .status-suspended { color: #f59e0b; }
        .tier-trial { background: #fef3c7; color: #d97706; }
        .tier-basic { background: #dbeafe; color: #2563eb; }
        .tier-professional { background: #dcfce7; color: #16a34a; }
        .tier-enterprise { background: #f3e8ff; color: #9333ea; }
        .dark .tier-trial { background: #d97706; color: #fef3c7; }
        .dark .tier-basic { background: #2563eb; color: #dbeafe; }
        .dark .tier-professional { background: #16a34a; color: #dcfce7; }
        .dark .tier-enterprise { background: #9333ea; color: #f3e8ff; }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200">
    <!-- Admin Header -->
    <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-2xl text-linkedin-blue mr-3"></i>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">GraphSearch Admin</h1>
                    <span class="ml-3 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">AUTHENTICATED</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-user mr-1"></i><?php echo htmlspecialchars($username); ?>
                        <span class="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded"><?php echo htmlspecialchars($role); ?></span>
                    </div>
                    <button id="toggleTheme" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="fas fa-sun hidden dark:inline"></i>
                    </button>
                    <a href="dashboard_modern.php" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                    </a>
                    <a href="login_modern.php?logout=1" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                        <i class="fas fa-sign-out-alt mr-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Success Message -->
        <div class="mb-8 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            <i class="fas fa-check-circle mr-2"></i>
            <strong>GraphSearch Integration Complete!</strong> 
            The admin authentication system is working correctly and GraphSearch is fully accessible with database authentication.
        </div>

        <!-- Dashboard Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="admin-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-80">Total Licenses</p>
                        <p id="totalLicenses" class="text-3xl font-bold">0</p>
                    </div>
                    <i class="fas fa-key text-3xl opacity-60"></i>
                </div>
            </div>
            <div class="admin-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-80">Active Licenses</p>
                        <p id="activeLicenses" class="text-3xl font-bold">0</p>
                    </div>
                    <i class="fas fa-check-circle text-3xl opacity-60"></i>
                </div>
            </div>
            <div class="admin-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-80">Revenue (Monthly)</p>
                        <p id="monthlyRevenue" class="text-3xl font-bold">$0</p>
                    </div>
                    <i class="fas fa-dollar-sign text-3xl opacity-60"></i>
                </div>
            </div>
            <div class="admin-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-80">Database Status</p>
                        <p class="text-3xl font-bold">✅ Connected</p>
                    </div>
                    <i class="fas fa-database text-3xl opacity-60"></i>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- License Generator -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-xl font-semibold mb-6 flex items-center">
                        <i class="fas fa-plus-circle mr-2 text-green-500"></i>Generate New License
                    </h2>

                    <form id="licenseForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">License Type</label>
                            <select id="licenseType" class="w-full p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                                <option value="basic">Basic ($29/mo)</option>
                                <option value="professional">Professional ($79/mo)</option>
                                <option value="enterprise">Enterprise ($199/mo)</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">Customer Email</label>
                            <input type="email" id="customerEmail" placeholder="<EMAIL>" class="w-full p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">Customer Name</label>
                            <input type="text" id="customerName" placeholder="John Doe" class="w-full p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">Duration</label>
                            <select id="licenseDuration" class="w-full p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                                <option value="1">1 Month</option>
                                <option value="3">3 Months</option>
                                <option value="6">6 Months</option>
                                <option value="12" selected>12 Months</option>
                                <option value="24">24 Months</option>
                                <option value="36">36 Months</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">Max Devices</label>
                            <select id="maxDevices" class="w-full p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                                <option value="1">1 Device</option>
                                <option value="2">2 Devices</option>
                                <option value="3" selected>3 Devices</option>
                                <option value="5">5 Devices</option>
                                <option value="10">10 Devices</option>
                                <option value="25">25 Devices</option>
                                <option value="50">50 Devices</option>
                                <option value="-1">Unlimited Devices</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">Notes (Optional)</label>
                            <textarea id="licenseNotes" rows="3" placeholder="Internal notes..." class="w-full p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600"></textarea>
                        </div>

                        <button type="submit" id="generateBtn" class="w-full bg-green-500 text-white py-3 rounded-lg hover:bg-green-600 transition-colors font-medium">
                            <i class="fas fa-key mr-2"></i>Generate License Key
                        </button>

                        <button type="button" id="generateTestBtn" onclick="generateTestLicense()" class="w-full mt-2 bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors font-medium">
                            <i class="fas fa-flask mr-2"></i>Generate Test License
                        </button>
                    </form>

                    <!-- Generated License Display -->
                    <div id="generatedLicense" class="hidden mt-6 p-4 bg-green-50 dark:bg-green-900 rounded-lg border border-green-200 dark:border-green-700">
                        <h3 class="font-semibold text-green-800 dark:text-green-200 mb-2">License Generated Successfully!</h3>
                        <div class="space-y-2">
                            <div>
                                <label class="text-sm font-medium text-green-700 dark:text-green-300">License Key:</label>
                                <div class="license-key mt-1" id="newLicenseKey">XXXX-XXXX-XXXX-XXXX</div>
                            </div>
                            <div class="flex gap-2 mt-3">
                                <button id="copyLicenseKey" class="flex-1 bg-green-500 text-white py-2 px-3 rounded text-sm hover:bg-green-600">
                                    <i class="fas fa-copy mr-1"></i>Copy Key
                                </button>
                                <button id="emailLicenseKey" class="flex-1 bg-blue-500 text-white py-2 px-3 rounded text-sm hover:bg-blue-600">
                                    <i class="fas fa-envelope mr-1"></i>Email Customer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- License Management -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-semibold flex items-center">
                            <i class="fas fa-list mr-2 text-blue-500"></i>License Management
                        </h2>
                        <div class="flex gap-2">
                            <button id="exportLicenses" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                                <i class="fas fa-download mr-2"></i>Export
                            </button>
                            <button id="refreshLicenses" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                <i class="fas fa-sync-alt mr-2"></i>Refresh
                            </button>
                        </div>
                    </div>

                    <!-- Search and Filter -->
                    <div class="flex gap-4 mb-6">
                        <div class="flex-1">
                            <input type="text" id="searchLicenses" placeholder="Search by email, name, or license key..." class="w-full p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                        </div>
                        <select id="filterStatus" class="p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="expired">Expired</option>
                            <option value="suspended">Suspended</option>
                        </select>
                        <select id="filterType" class="p-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                            <option value="">All Types</option>
                            <option value="basic">Basic</option>
                            <option value="professional">Professional</option>
                            <option value="enterprise">Enterprise</option>
                        </select>
                    </div>

                    <!-- License Table -->
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-200 dark:border-gray-700">
                                    <th class="text-left py-3 px-2 font-medium text-gray-600 dark:text-gray-400">Customer</th>
                                    <th class="text-left py-3 px-2 font-medium text-gray-600 dark:text-gray-400">License Key</th>
                                    <th class="text-left py-3 px-2 font-medium text-gray-600 dark:text-gray-400">Type</th>
                                    <th class="text-left py-3 px-2 font-medium text-gray-600 dark:text-gray-400">Status</th>
                                    <th class="text-left py-3 px-2 font-medium text-gray-600 dark:text-gray-400">Expires</th>
                                    <th class="text-left py-3 px-2 font-medium text-gray-600 dark:text-gray-400">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="licensesTableBody">
                                <tr>
                                    <td colspan="6" class="text-center py-8 text-gray-500 dark:text-gray-400">
                                        <i class="fas fa-key text-4xl mb-2"></i>
                                        <div>No licenses generated yet</div>
                                        <div class="text-sm">Generate your first license above</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Integration Status -->
        <div class="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">System Integration Status</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-database text-green-600 text-2xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-800 dark:text-white">Database</h4>
                    <p class="text-sm text-green-600">Connected & Operational</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-user-shield text-blue-600 text-2xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-800 dark:text-white">Authentication</h4>
                    <p class="text-sm text-blue-600">Secure & Active</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-search text-purple-600 text-2xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-800 dark:text-white">GraphSearch</h4>
                    <p class="text-sm text-purple-600">Fully Integrated</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Theme toggle
            $('#toggleTheme').click(function() {
                $('html').toggleClass('dark');
                localStorage.setItem('adminTheme', $('html').hasClass('dark') ? 'dark' : 'light');
            });

            // Load saved theme
            if (localStorage.getItem('adminTheme') === 'dark') {
                $('html').addClass('dark');
            }

            // License generation functionality (simplified for demo)
            $('#licenseForm').on('submit', function(e) {
                e.preventDefault();
                generateLicense();
            });

            function generateLicense() {
                const type = $('#licenseType').val();
                const email = $('#customerEmail').val().trim();
                const name = $('#customerName').val().trim();
                const duration = $('#licenseDuration').val();
                const maxDevices = $('#maxDevices').val();

                if (!email || !name) {
                    alert('Please fill in customer email and name');
                    return;
                }

                // Generate a demo license key
                const licenseKey = generateDemoLicenseKey();
                
                // Show generated license
                $('#newLicenseKey').text(licenseKey);
                $('#generatedLicense').removeClass('hidden');

                // Add to demo table
                addLicenseToTable({
                    name: name,
                    email: email,
                    key: licenseKey,
                    type: type,
                    status: 'active',
                    expires: new Date(Date.now() + (duration * 30 * 24 * 60 * 60 * 1000)).toLocaleDateString()
                });

                // Reset form
                $('#licenseForm')[0].reset();
                $('#licenseType').val('basic');
                $('#licenseDuration').val('12');
                $('#maxDevices').val('3');

                updateStats();
            }

            function generateDemoLicenseKey() {
                const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                let result = '';
                for (let i = 0; i < 20; i++) {
                    if (i > 0 && i % 4 === 0) result += '-';
                    result += chars.charAt(Math.floor(Math.random() * chars.length));
                }
                return result;
            }

            function addLicenseToTable(license) {
                if ($('#licensesTableBody tr').first().find('td').attr('colspan')) {
                    $('#licensesTableBody').empty();
                }

                const row = `
                    <tr class="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="py-3 px-2">
                            <div class="font-medium">${license.name}</div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">${license.email}</div>
                        </td>
                        <td class="py-3 px-2">
                            <div class="license-key text-xs font-mono">${license.key}</div>
                        </td>
                        <td class="py-3 px-2">
                            <span class="px-2 py-1 rounded-full text-xs font-medium tier-${license.type}">
                                ${license.type.charAt(0).toUpperCase() + license.type.slice(1)}
                            </span>
                        </td>
                        <td class="py-3 px-2">
                            <span class="status-${license.status}">
                                <i class="fas fa-circle text-xs mr-1"></i>
                                ${license.status.charAt(0).toUpperCase() + license.status.slice(1)}
                            </span>
                        </td>
                        <td class="py-3 px-2">
                            <div class="text-sm">${license.expires}</div>
                        </td>
                        <td class="py-3 px-2">
                            <button onclick="copyToClipboard('${license.key}')" class="text-green-500 hover:text-green-700 p-1" title="Copy Key">
                                <i class="fas fa-copy"></i>
                            </button>
                        </td>
                    </tr>
                `;
                $('#licensesTableBody').append(row);
            }

            function updateStats() {
                const total = $('#licensesTableBody tr').length;
                $('#totalLicenses').text(total);
                $('#activeLicenses').text(total);
                $('#monthlyRevenue').text('$' + (total * 79).toLocaleString());
            }

            window.generateTestLicense = function() {
                $('#customerEmail').val('<EMAIL>');
                $('#customerName').val('Test User');
                $('#licenseType').val('professional');
                generateLicense();
            };

            window.copyToClipboard = function(text) {
                navigator.clipboard.writeText(text).then(function() {
                    alert('License key copied to clipboard!');
                });
            };

            // Copy license key button
            $('#copyLicenseKey').click(function() {
                const licenseKey = $('#newLicenseKey').text();
                copyToClipboard(licenseKey);
            });

            // Email license key button
            $('#emailLicenseKey').click(function() {
                const licenseKey = $('#newLicenseKey').text();
                const email = $('#customerEmail').val();
                const name = $('#customerName').val();
                
                const subject = encodeURIComponent('Your GraphSearch License Key');
                const body = encodeURIComponent(`Dear ${name},\n\nYour license key is: ${licenseKey}\n\nThank you for your purchase!`);
                
                window.open(`mailto:${email}?subject=${subject}&body=${body}`, '_blank');
            });

            // Export functionality
            $('#exportLicenses').click(function() {
                alert('Export functionality would generate an Excel file with all licenses.');
            });

            // Refresh functionality
            $('#refreshLicenses').click(function() {
                alert('Licenses refreshed!');
            });
        });
    </script>
</body>
</html>
